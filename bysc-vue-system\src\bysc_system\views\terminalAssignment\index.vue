
<template>
  <div>
    <el-row>
      <el-col :span="24">
        <Grid
          api="tenant/tenant-page"
          :event-bus="searchEventBus"
          :search-params="searchForm"
          :newcolumn="columns"
          @datas="getDatas"
          @columnChange="getColumn"
          ref="grid"
        >
          <div slot="search">
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="租户编码">
                <el-input
                  style="width: 200px;margin:0 10px 0 0;"
                  v-model.trim="searchForm.tenantCode"
                  size="small"
                  placeholder="请输入租户编码"
                ></el-input>
              </el-form-item>
              <el-form-item label="租户名称">
                <el-input
                  style="width: 200px;margin:0 10px 0 0;"
                  v-model.trim="searchForm.tenantName"
                  size="small"
                  placeholder="请输入租户名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="租户管理员">
                <el-input
                  style="width: 200px;margin:0 10px 0 0;"
                  v-model.trim="searchForm.tenantAdmin"
                  size="small"
                  placeholder="请输入租户管理员"
                ></el-input>
              </el-form-item>
              <el-form-item label="是否选中">
                <el-select
                  size="small"
                  clearable
                  @keydown.enter.native.prevent="searchTable"
                  v-model="searchForm.isSelected"
                  placeholder="请选择是否选中"
                >
                  <el-option :label="'是'" :value="true"> </el-option>
                  <el-option :label="'否'" :value="false"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  size="small"
                  type="primary"
                  style="margin: 0 0 0 10px"
                  @click="searchTable"
                  >搜索</el-button>
                <el-button size="small" @click="resetTable">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="action">
            <el-button v-permission="'tenant_add'" size="small" type="primary" @click="handleAdd">新建租户</el-button>
            <el-button v-permission="'tenant_batchDel'" size="small" @click="batchDelete">删除租户</el-button>
          </div>
          <el-table slot="table" slot-scope="{}" v-loading="tableLoading" ref="multipleTable" :data="tableData" stripe style="width: 100%">
            <el-table-column fixed="left" :align="'center'" type="selection" width="55">
            </el-table-column>
            <el-table-column fixed="left" :align="'center'" label="序号" type="index" width="50">
            </el-table-column>
            <template v-for="(item, index) in columns">
              <el-table-column
                v-if="item.slot === 'isSelected'"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="100"
              >
                <template slot-scope="scope">
                  <el-switch
                    v-model="scope.row.isSelected"
                    @change="handleSelectionChange(scope.row)"
                    active-text="是"
                    inactive-text="否">
                  </el-switch>
                </template>
              </el-table-column>
              <el-table-column
                v-else-if="item.slot"
                :show-overflow-tooltip="true"
                :align="item.align ? item.align : 'center'"
                :key="index"
                :prop="item.key"
                :label="item.title"
                min-width="180"
              >
                <template slot-scope="scope">
                  <el-tag :type="scope.row[item.slot]?'success':'danger'">{{scope.row[item.slot]?'启用':'禁用'}}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                v-else
                :show-overflow-tooltip="true"
                :key="item.key"
                :prop="item.key"
                :label="item.title"
                :min-width="item.width ? item.width : '150'"
                :align="item.align ? item.align : 'center'"
              >
              </el-table-column>
            </template>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              type="action"
              width="200"
            >
              <template slot-scope="scope">
                <template>
                  <el-button
                    v-permission="'tenant_edit'"
                    style="margin-right:6px"
                    @click="handleEdit(scope.row)"
                    type="text"
                    size="small"
                    >编辑</el-button
                  >
                </template>

                <template>

                  <el-button
                    v-permission="'tenant_admin_manage'"
                    style="margin-right:6px"
                    @click="handleAdminManage(scope.row)"
                    type="text"
                    size="small"
                    >维护管理员</el-button
                  >
                </template>

                <template>
                  <el-popconfirm
                  @confirm="handleDelete(scope.row.id)"
                    title="您确定要删除该租户吗？">
                    <el-button v-permission="'tenant_del'" type="text" size="small" slot="reference">删除</el-button>
                  </el-popconfirm>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </Grid>
      </el-col>
    </el-row>
    <el-drawer
      size="50%"
      :title="drawerName"
      :visible.sync="drawer"
      :direction="direction"
      :wrapperClosable="false"
    >
      <TenantForm
        v-model="tenantRuleForm"
        :is-edit="isEditMode"
        :loading="submitLoading"
        @submit="handleFormSubmit"
        @cancel="handleFormCancel"
        ref="tenantForm"
      />
    </el-drawer>

    <!-- 维护管理员弹窗 -->
    <AdminManageDialog
      :visible.sync="adminManageVisible"
      :tenant-info="currentTenant"
    />
  </div>
</template>

<script>
import Vue from 'vue';
import Grid from '@/components/Grid';
import TenantForm from './components/TenantForm.vue';
import AdminManageDialog from './components/AdminManageDialog.vue';
import LoadingFix from '@/utils/loading-fix';
const defaultSearchForm = {
  tenantCode: '',
  tenantName: '',
  tenantAdmin: '',
  isSelected: ''
};
const defaultConfigForm = {
  "id": 0,
  "tenantCode": "",
  "tenantName": "",
  "tenantAdmin": "",
  "comments": "",
  "isSelected": false
};
export default {
  components: {Grid, TenantForm, AdminManageDialog},
  destroyed() {
    this.searchEventBus.$off();
    this.searchConfigEventBus.$off();

    // 清理遮罩层观察器
    if (this.maskObserver) {
      this.maskObserver.disconnect();
    }
  },
  data() {
    this.searchEventBus = new Vue();
    this.searchConfigEventBus = new Vue();
    return {
      configDrawer: false,
      configDrawerName: '添加',
      configStatus: true,
      tenantRuleForm: Object.assign({}, defaultConfigForm),
      isEditMode: false,
      submitLoading: false,
      tableLoading: false,
      configTableData: [],
      itemId: null,
      configDialogName: '',
      configDialog: false,
      status: true,
      rules: {
        dictName: [
          {required: true, message: '请输入字典名称', trigger: 'change,blur'}
        ],
        dictCode: [
          {required: true, message: '请输入字典代码', trigger: 'change,blur'}
        ],
        dictOrder: [
          {required: true, message: '请输入字典排序', trigger: 'change,blur'},
        ],
      },
      drawerName: '添加',
      drawer: false,
      direction: 'rtl',
      searchForm: Object.assign({}, defaultSearchForm),
      columns: [
        {
          title: '租户编码',
          key: 'tenantCode',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '租户名称',
          key: 'tenantName',
          tooltip: true,
          minWidth: 130,
        },
        {
          title: '租户管理员',
          key: 'tenantAdmin',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '备注',
          key: 'comments',
          tooltip: true,
          minWidth: 200,
        },
        {
          title: '是否选中',
          slot: 'isSelected',
          tooltip: true,
          minWidth: 100,
        },
      ],
      tableData: [],
      // 维护管理员相关
      adminManageVisible: false,
      currentTenant: {}
    };
  },
  mounted() {
    // 确保页面加载时重置所有loading状态
    this.tableLoading = false;

    // 使用 LoadingFix 工具清理遮罩层
    this.$nextTick(() => {
      setTimeout(() => {
        LoadingFix.forceClearMasks();
        this.tableLoading = false;
        console.log('已使用 LoadingFix 重置页面loading状态');
      }, 100);
    });

    // 启动自动清理监听
    this.maskObserver = LoadingFix.startAutoCleanup();
  },

  methods: {
    // 获取数据时的回调
    getDatas(data) {
      this.tableData = data;
      this.tableLoading = false; // 确保数据加载完成后隐藏loading
    },

    handleSelectionChange(row) {
      // 处理选中状态变化
      console.log('选中状态变化:', row.tenantName, row.isSelected);
      // 这里可以添加API调用来保存选中状态
      // this.$api['tenant/update-selection']({id: row.id, isSelected: row.isSelected})
    },

    handleAdd() {
      this.drawer = true;
      // 使用浅拷贝替代深拷贝，提高性能
      this.tenantRuleForm = Object.assign({}, defaultConfigForm);
      this.drawerName = '新建租户';
      this.isEditMode = false;
    },
    handleEdit(row) {
      this.tenantRuleForm = Object.assign({}, row);
      this.drawer = true;
      this.drawerName = '编辑租户';
      this.isEditMode = true;
    },
    handleAdminManage(row) {
      this.currentTenant = Object.assign({}, row);
      this.adminManageVisible = true;
    },
    batchDelete() {
      let ids = [];
      this.$refs.multipleTable.selection.forEach(e => {
        ids.push(e.id);
      });
      console.info(ids, this.$refs.multipleTable.selection);
      if (!ids.length) {
        this.$message.info('请先选择您要删除的项');
        return;
      }
      this.$confirm('此操作将删除租户信息?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api['tenant/tenant-delete']({ids: ids}).then(data => {
          this.$refs.grid.query();
          this.$message({
            message: '删除成功',
            type: 'success'
          });
        });
      }).catch(err => {});
    },
    handleDelete(e) {
      this.$api['tenant/tenant-delete']({ids: [e]}).then(data => {
        this.$refs.grid.query();
        this.$message({
          message: '删除成功',
          type: 'success'
        });
      });
    },

    searchTable() {
      this.$refs.grid.query();
    },
    resetTable() {
      // 使用浅拷贝替代深拷贝，提高性能
      this.searchForm = Object.assign({}, defaultSearchForm);
      this.$nextTick(() => {
        this.$refs.grid.query();
      });
    },
    getColumn(e) {
      this.columns = e;
    },
    handleConfigEdit(row) {
      this.configStatus = !!row.dictStatus;
      this.tenantRuleForm = Object.assign({}, row);
      this.configDrawer = true;
      this.configDrawerName = '编辑';
    },
    handleConfigDelete(e) {
      this.$api['sysDict/dict-item-delete']({ids: [e]}).then(data => {
        this.$refs.configGrid.query();
        this.$message({
          message: '删除成功',
          type: 'success'
        });
      });
    },
    // 表单提交处理
    handleFormSubmit(formData) {
      this.submitLoading = true;
      this.$api['tenant/tenant-save'](formData).then(data => {
        this.$message({
          message: '保存成功',
          type: 'success'
        });
        this.drawer = false;
        this.$refs.grid.query();
        this.resetFormData();
      }).catch(error => {
        console.error('保存失败:', error);
      }).finally(() => {
        this.submitLoading = false;
      });
    },

    // 表单取消处理
    handleFormCancel() {
      this.drawer = false;
      this.resetFormData();
    },

    // 重置表单数据
    resetFormData() {
      // 使用浅拷贝替代深拷贝，提高性能
      this.tenantRuleForm = Object.assign({}, defaultConfigForm);
      this.isEditMode = false;
      this.submitLoading = false;
    },
  },
};
</script>
<style lang="less" scoped></style>
