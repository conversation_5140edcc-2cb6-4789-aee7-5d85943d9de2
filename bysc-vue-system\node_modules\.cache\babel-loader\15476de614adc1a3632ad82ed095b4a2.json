{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\index.vue", "mtime": 1753841087327}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import Vue from 'vue';\nimport Grid from '@/components/Grid';\nimport LoadingFix from '@/utils/loading-fix';\nvar defaultSearchForm = {\n  terminalId: '',\n  terminalIp: '',\n  terminalName: '',\n  status: '',\n  assignStatus: ''\n};\nvar defaultAssignForm = {\n  terminalId: '',\n  terminalName: '',\n  tenantId: ''\n};\nexport default {\n  components: {\n    Grid: Grid\n  },\n  destroyed: function destroyed() {\n    this.searchEventBus.$off();\n\n    // 清理遮罩层观察器\n    if (this.maskObserver) {\n      this.maskObserver.disconnect();\n    }\n  },\n  data: function data() {\n    this.searchEventBus = new Vue();\n    return {\n      submitLoading: false,\n      tableLoading: false,\n      searchForm: Object.assign({}, defaultSearchForm),\n      columns: [{\n        title: '终端ID',\n        key: 'terminalId',\n        tooltip: true,\n        minWidth: 120\n      }, {\n        title: '终端IP',\n        key: 'terminalIp',\n        tooltip: true,\n        minWidth: 150\n      }, {\n        title: '终端名称',\n        key: 'terminalName',\n        tooltip: true,\n        minWidth: 150\n      }, {\n        title: '终端上线时间',\n        key: 'onlineTime',\n        tooltip: true,\n        minWidth: 180\n      }, {\n        title: '状态',\n        key: 'status',\n        slot: 'status',\n        tooltip: true,\n        minWidth: 100\n      }, {\n        title: '分配状态',\n        key: 'assignStatus',\n        slot: 'assignStatus',\n        tooltip: true,\n        minWidth: 100\n      }, {\n        title: '当前租户',\n        key: 'currentTenant',\n        tooltip: true,\n        minWidth: 150\n      }],\n      tableData: [],\n      // 分配租户相关\n      assignDialogVisible: false,\n      dialogTitle: '',\n      assignForm: Object.assign({}, defaultAssignForm),\n      assignRules: {\n        tenantId: [{\n          required: true,\n          message: '请选择租户',\n          trigger: 'change'\n        }]\n      },\n      tenantList: [],\n      // 租户列表\n      currentTerminal: {} // 当前操作的终端\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n    // 确保页面加载时重置所有loading状态\n    this.tableLoading = false;\n\n    // 使用 LoadingFix 工具清理遮罩层\n    this.$nextTick(function () {\n      setTimeout(function () {\n        LoadingFix.forceClearMasks();\n        _this.tableLoading = false;\n        console.log('已使用 LoadingFix 重置页面loading状态');\n      }, 100);\n    });\n\n    // 启动自动清理监听\n    this.maskObserver = LoadingFix.startAutoCleanup();\n  },\n  methods: {\n    // 获取数据时的回调\n    getDatas: function getDatas(data) {\n      this.tableData = data;\n      this.tableLoading = false; // 确保数据加载完成后隐藏loading\n    },\n    // 分配租户\n    handleAssignTenant: function handleAssignTenant(row) {\n      this.currentTerminal = Object.assign({}, row);\n      this.assignForm = {\n        terminalId: row.terminalId,\n        terminalName: row.terminalName,\n        tenantId: ''\n      };\n      this.dialogTitle = '分配租户';\n      this.assignDialogVisible = true;\n      this.loadTenantList();\n    },\n    // 更改租户\n    handleChangeTenant: function handleChangeTenant(row) {\n      this.currentTerminal = Object.assign({}, row);\n      this.assignForm = {\n        terminalId: row.terminalId,\n        terminalName: row.terminalName,\n        tenantId: row.tenantId || ''\n      };\n      this.dialogTitle = '更改租户';\n      this.assignDialogVisible = true;\n      this.loadTenantList();\n    },\n    // 加载租户列表\n    loadTenantList: function loadTenantList() {\n      var _this2 = this;\n      this.$api['tenant/tenant-list']().then(function (res) {\n        _this2.tenantList = res.data || [];\n      }).catch(function (error) {\n        console.error('加载租户列表失败:', error);\n        _this2.tenantList = [];\n      });\n    },\n    // 确认分配\n    confirmAssign: function confirmAssign() {\n      var _this3 = this;\n      this.$refs.assignForm.validate(function (valid) {\n        if (valid) {\n          _this3.submitLoading = true;\n          var apiData = {\n            terminalId: _this3.assignForm.terminalId,\n            tenantId: _this3.assignForm.tenantId\n          };\n          _this3.$api['terminal/assign-tenant'](apiData).then(function () {\n            _this3.$message({\n              message: '分配成功',\n              type: 'success'\n            });\n            _this3.assignDialogVisible = false;\n            _this3.$refs.grid.query();\n          }).catch(function (error) {\n            console.error('分配失败:', error);\n          }).finally(function () {\n            _this3.submitLoading = false;\n          });\n        }\n      });\n    },\n    searchTable: function searchTable() {\n      this.$refs.grid.query();\n    },\n    resetTable: function resetTable() {\n      var _this4 = this;\n      this.searchForm = Object.assign({}, defaultSearchForm);\n      this.$nextTick(function () {\n        _this4.$refs.grid.query();\n      });\n    },\n    getColumn: function getColumn(e) {\n      this.columns = e;\n    }\n  }\n};", {"version": 3, "names": ["<PERSON><PERSON>", "Grid", "LoadingFix", "defaultSearchForm", "terminalId", "terminalIp", "terminalName", "status", "assignStatus", "defaultAssignForm", "tenantId", "components", "destroyed", "searchEventBus", "$off", "maskObserver", "disconnect", "data", "submitLoading", "tableLoading", "searchForm", "Object", "assign", "columns", "title", "key", "tooltip", "min<PERSON><PERSON><PERSON>", "slot", "tableData", "assignDialogVisible", "dialogTitle", "assignForm", "assignRules", "required", "message", "trigger", "tenantList", "currentTerminal", "mounted", "_this", "$nextTick", "setTimeout", "forceClearMasks", "console", "log", "startAutoCleanup", "methods", "getDatas", "handleAssignTenant", "row", "loadTenantList", "handleChangeTenant", "_this2", "$api", "then", "res", "catch", "error", "confirmAssign", "_this3", "$refs", "validate", "valid", "apiData", "$message", "type", "grid", "query", "finally", "searchTable", "resetTable", "_this4", "getColumn", "e"], "sources": ["src/bysc_system/views/terminalAssignment/index.vue"], "sourcesContent": ["\r\n<template>\r\n  <div>\r\n    <el-row>\r\n      <el-col :span=\"24\">\r\n        <Grid\r\n          api=\"terminal/terminal-page\"\r\n          :event-bus=\"searchEventBus\"\r\n          :search-params=\"searchForm\"\r\n          :newcolumn=\"columns\"\r\n          @datas=\"getDatas\"\r\n          @columnChange=\"getColumn\"\r\n          ref=\"grid\"\r\n        >\r\n          <div slot=\"search\">\r\n            <el-form\r\n              :inline=\"true\"\r\n              :model=\"searchForm\"\r\n              class=\"demo-form-inline\"\r\n            >\r\n              <el-form-item label=\"终端ID\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.terminalId\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入终端ID\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"终端IP\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.terminalIp\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入终端IP\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"终端名称\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.terminalName\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入终端名称\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"状态\">\r\n                <el-select\r\n                  size=\"small\"\r\n                  clearable\r\n                  @keydown.enter.native.prevent=\"searchTable\"\r\n                  v-model=\"searchForm.status\"\r\n                  placeholder=\"请选择状态\"\r\n                >\r\n                  <el-option label=\"在线\" value=\"online\"></el-option>\r\n                  <el-option label=\"离线\" value=\"offline\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item label=\"分配状态\">\r\n                <el-select\r\n                  size=\"small\"\r\n                  clearable\r\n                  @keydown.enter.native.prevent=\"searchTable\"\r\n                  v-model=\"searchForm.assignStatus\"\r\n                  placeholder=\"请选择分配状态\"\r\n                >\r\n                  <el-option label=\"已分配\" value=\"assigned\"></el-option>\r\n                  <el-option label=\"待分配\" value=\"pending\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"primary\"\r\n                  style=\"margin: 0 0 0 10px\"\r\n                  @click=\"searchTable\"\r\n                  >搜索</el-button>\r\n                <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n          <el-table slot=\"table\" slot-scope=\"{}\" v-loading=\"tableLoading\" ref=\"multipleTable\" :data=\"tableData\" stripe style=\"width: 100%\">\r\n            <el-table-column fixed=\"left\" :align=\"'center'\" label=\"序号\" type=\"index\" width=\"50\">\r\n            </el-table-column>\r\n            <template v-for=\"(item, index) in columns\">\r\n              <el-table-column\r\n                v-if=\"item.slot === 'status'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"100\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"scope.row.status === 'online' ? 'success' : 'danger'\">\r\n                    {{ scope.row.status === 'online' ? '在线' : '离线' }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else-if=\"item.slot === 'assignStatus'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"100\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"scope.row.assignStatus === 'assigned' ? 'success' : 'warning'\">\r\n                    {{ scope.row.assignStatus === 'assigned' ? '已分配' : '待分配' }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else\r\n                :show-overflow-tooltip=\"true\"\r\n                :key=\"item.key\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                :min-width=\"item.width ? item.width : '150'\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n              >\r\n              </el-table-column>\r\n            </template>\r\n            <el-table-column\r\n              fixed=\"right\"\r\n              align=\"center\"\r\n              label=\"操作\"\r\n              type=\"action\"\r\n              width=\"200\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"scope.row.assignStatus === 'assigned'\"\r\n                  style=\"margin-right:6px\"\r\n                  @click=\"handleChangeTenant(scope.row)\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  >更改租户</el-button>\r\n                <el-button\r\n                  v-if=\"scope.row.assignStatus === 'pending'\"\r\n                  style=\"margin-right:6px\"\r\n                  @click=\"handleAssignTenant(scope.row)\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  >分配租户</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </Grid>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 分配租户弹窗 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"assignDialogVisible\"\r\n      width=\"500px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-form :model=\"assignForm\" :rules=\"assignRules\" ref=\"assignForm\" label-width=\"100px\">\r\n        <el-form-item label=\"终端ID\">\r\n          <el-input v-model=\"assignForm.terminalId\" disabled></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"终端名称\">\r\n          <el-input v-model=\"assignForm.terminalName\" disabled></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"选择租户\" prop=\"tenantId\">\r\n          <el-select v-model=\"assignForm.tenantId\" placeholder=\"请选择租户\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"tenant in tenantList\"\r\n              :key=\"tenant.id\"\r\n              :label=\"tenant.tenantName\"\r\n              :value=\"tenant.id\">\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"assignDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmAssign\" :loading=\"submitLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport Grid from '@/components/Grid';\r\nimport LoadingFix from '@/utils/loading-fix';\r\n\r\nconst defaultSearchForm = {\r\n  terminalId: '',\r\n  terminalIp: '',\r\n  terminalName: '',\r\n  status: '',\r\n  assignStatus: ''\r\n};\r\n\r\nconst defaultAssignForm = {\r\n  terminalId: '',\r\n  terminalName: '',\r\n  tenantId: ''\r\n};\r\n\r\nexport default {\r\n  components: {Grid},\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n\r\n    // 清理遮罩层观察器\r\n    if (this.maskObserver) {\r\n      this.maskObserver.disconnect();\r\n    }\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    return {\r\n      submitLoading: false,\r\n      tableLoading: false,\r\n      searchForm: Object.assign({}, defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: '终端ID',\r\n          key: 'terminalId',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '终端IP',\r\n          key: 'terminalIp',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '终端名称',\r\n          key: 'terminalName',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '终端上线时间',\r\n          key: 'onlineTime',\r\n          tooltip: true,\r\n          minWidth: 180,\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '分配状态',\r\n          key: 'assignStatus',\r\n          slot: 'assignStatus',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '当前租户',\r\n          key: 'currentTenant',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n      ],\r\n      tableData: [],\r\n      // 分配租户相关\r\n      assignDialogVisible: false,\r\n      dialogTitle: '',\r\n      assignForm: Object.assign({}, defaultAssignForm),\r\n      assignRules: {\r\n        tenantId: [\r\n          {required: true, message: '请选择租户', trigger: 'change'}\r\n        ]\r\n      },\r\n      tenantList: [], // 租户列表\r\n      currentTerminal: {} // 当前操作的终端\r\n    };\r\n  },\r\n  mounted() {\r\n    // 确保页面加载时重置所有loading状态\r\n    this.tableLoading = false;\r\n\r\n    // 使用 LoadingFix 工具清理遮罩层\r\n    this.$nextTick(() => {\r\n      setTimeout(() => {\r\n        LoadingFix.forceClearMasks();\r\n        this.tableLoading = false;\r\n        console.log('已使用 LoadingFix 重置页面loading状态');\r\n      }, 100);\r\n    });\r\n\r\n    // 启动自动清理监听\r\n    this.maskObserver = LoadingFix.startAutoCleanup();\r\n  },\r\n\r\n  methods: {\r\n    // 获取数据时的回调\r\n    getDatas(data) {\r\n      this.tableData = data;\r\n      this.tableLoading = false; // 确保数据加载完成后隐藏loading\r\n    },\r\n\r\n    // 分配租户\r\n    handleAssignTenant(row) {\r\n      this.currentTerminal = Object.assign({}, row);\r\n      this.assignForm = {\r\n        terminalId: row.terminalId,\r\n        terminalName: row.terminalName,\r\n        tenantId: ''\r\n      };\r\n      this.dialogTitle = '分配租户';\r\n      this.assignDialogVisible = true;\r\n      this.loadTenantList();\r\n    },\r\n\r\n    // 更改租户\r\n    handleChangeTenant(row) {\r\n      this.currentTerminal = Object.assign({}, row);\r\n      this.assignForm = {\r\n        terminalId: row.terminalId,\r\n        terminalName: row.terminalName,\r\n        tenantId: row.tenantId || ''\r\n      };\r\n      this.dialogTitle = '更改租户';\r\n      this.assignDialogVisible = true;\r\n      this.loadTenantList();\r\n    },\r\n\r\n    // 加载租户列表\r\n    loadTenantList() {\r\n      this.$api['tenant/tenant-list']().then(res => {\r\n        this.tenantList = res.data || [];\r\n      }).catch(error => {\r\n        console.error('加载租户列表失败:', error);\r\n        this.tenantList = [];\r\n      });\r\n    },\r\n\r\n    // 确认分配\r\n    confirmAssign() {\r\n      this.$refs.assignForm.validate(valid => {\r\n        if (valid) {\r\n          this.submitLoading = true;\r\n          const apiData = {\r\n            terminalId: this.assignForm.terminalId,\r\n            tenantId: this.assignForm.tenantId\r\n          };\r\n\r\n          this.$api['terminal/assign-tenant'](apiData).then(() => {\r\n            this.$message({\r\n              message: '分配成功',\r\n              type: 'success'\r\n            });\r\n            this.assignDialogVisible = false;\r\n            this.$refs.grid.query();\r\n          }).catch(error => {\r\n            console.error('分配失败:', error);\r\n          }).finally(() => {\r\n            this.submitLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n\r\n    resetTable() {\r\n      this.searchForm = Object.assign({}, defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n\r\n    getColumn(e) {\r\n      this.columns = e;\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped></style>\r\n"], "mappings": "AA2LA,OAAAA,GAAA;AACA,OAAAC,IAAA;AACA,OAAAC,UAAA;AAEA,IAAAC,iBAAA;EACAC,UAAA;EACAC,UAAA;EACAC,YAAA;EACAC,MAAA;EACAC,YAAA;AACA;AAEA,IAAAC,iBAAA;EACAL,UAAA;EACAE,YAAA;EACAI,QAAA;AACA;AAEA;EACAC,UAAA;IAAAV,IAAA,EAAAA;EAAA;EACAW,SAAA,WAAAA,UAAA;IACA,KAAAC,cAAA,CAAAC,IAAA;;IAEA;IACA,SAAAC,YAAA;MACA,KAAAA,YAAA,CAAAC,UAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA,KAAAJ,cAAA,OAAAb,GAAA;IACA;MACAkB,aAAA;MACAC,YAAA;MACAC,UAAA,EAAAC,MAAA,CAAAC,MAAA,KAAAnB,iBAAA;MACAoB,OAAA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAG,IAAA;QACAF,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAG,IAAA;QACAF,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,EACA;MACAE,SAAA;MACA;MACAC,mBAAA;MACAC,WAAA;MACAC,UAAA,EAAAX,MAAA,CAAAC,MAAA,KAAAb,iBAAA;MACAwB,WAAA;QACAvB,QAAA,GACA;UAAAwB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,UAAA;MAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,KAAArB,YAAA;;IAEA;IACA,KAAAsB,SAAA;MACAC,UAAA;QACAxC,UAAA,CAAAyC,eAAA;QACAH,KAAA,CAAArB,YAAA;QACAyB,OAAA,CAAAC,GAAA;MACA;IACA;;IAEA;IACA,KAAA9B,YAAA,GAAAb,UAAA,CAAA4C,gBAAA;EACA;EAEAC,OAAA;IACA;IACAC,QAAA,WAAAA,SAAA/B,IAAA;MACA,KAAAY,SAAA,GAAAZ,IAAA;MACA,KAAAE,YAAA;IACA;IAEA;IACA8B,kBAAA,WAAAA,mBAAAC,GAAA;MACA,KAAAZ,eAAA,GAAAjB,MAAA,CAAAC,MAAA,KAAA4B,GAAA;MACA,KAAAlB,UAAA;QACA5B,UAAA,EAAA8C,GAAA,CAAA9C,UAAA;QACAE,YAAA,EAAA4C,GAAA,CAAA5C,YAAA;QACAI,QAAA;MACA;MACA,KAAAqB,WAAA;MACA,KAAAD,mBAAA;MACA,KAAAqB,cAAA;IACA;IAEA;IACAC,kBAAA,WAAAA,mBAAAF,GAAA;MACA,KAAAZ,eAAA,GAAAjB,MAAA,CAAAC,MAAA,KAAA4B,GAAA;MACA,KAAAlB,UAAA;QACA5B,UAAA,EAAA8C,GAAA,CAAA9C,UAAA;QACAE,YAAA,EAAA4C,GAAA,CAAA5C,YAAA;QACAI,QAAA,EAAAwC,GAAA,CAAAxC,QAAA;MACA;MACA,KAAAqB,WAAA;MACA,KAAAD,mBAAA;MACA,KAAAqB,cAAA;IACA;IAEA;IACAA,cAAA,WAAAA,eAAA;MAAA,IAAAE,MAAA;MACA,KAAAC,IAAA,yBAAAC,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAAhB,UAAA,GAAAmB,GAAA,CAAAvC,IAAA;MACA,GAAAwC,KAAA,WAAAC,KAAA;QACAd,OAAA,CAAAc,KAAA,cAAAA,KAAA;QACAL,MAAA,CAAAhB,UAAA;MACA;IACA;IAEA;IACAsB,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAA7B,UAAA,CAAA8B,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAA1C,aAAA;UACA,IAAA8C,OAAA;YACA5D,UAAA,EAAAwD,MAAA,CAAA5B,UAAA,CAAA5B,UAAA;YACAM,QAAA,EAAAkD,MAAA,CAAA5B,UAAA,CAAAtB;UACA;UAEAkD,MAAA,CAAAN,IAAA,2BAAAU,OAAA,EAAAT,IAAA;YACAK,MAAA,CAAAK,QAAA;cACA9B,OAAA;cACA+B,IAAA;YACA;YACAN,MAAA,CAAA9B,mBAAA;YACA8B,MAAA,CAAAC,KAAA,CAAAM,IAAA,CAAAC,KAAA;UACA,GAAAX,KAAA,WAAAC,KAAA;YACAd,OAAA,CAAAc,KAAA,UAAAA,KAAA;UACA,GAAAW,OAAA;YACAT,MAAA,CAAA1C,aAAA;UACA;QACA;MACA;IACA;IAEAoD,WAAA,WAAAA,YAAA;MACA,KAAAT,KAAA,CAAAM,IAAA,CAAAC,KAAA;IACA;IAEAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAApD,UAAA,GAAAC,MAAA,CAAAC,MAAA,KAAAnB,iBAAA;MACA,KAAAsC,SAAA;QACA+B,MAAA,CAAAX,KAAA,CAAAM,IAAA,CAAAC,KAAA;MACA;IACA;IAEAK,SAAA,WAAAA,UAAAC,CAAA;MACA,KAAAnD,OAAA,GAAAmD,CAAA;IACA;EACA;AACA", "ignoreList": []}]}