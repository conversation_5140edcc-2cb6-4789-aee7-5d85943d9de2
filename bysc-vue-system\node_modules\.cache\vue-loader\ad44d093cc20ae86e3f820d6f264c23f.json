{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\index.vue?vue&type=template&id=10f0b2ce&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\index.vue", "mtime": 1753841087327}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745221315417}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\n<div>\n  <el-row>\n    <el-col :span=\"24\">\n      <Grid\n        api=\"terminal/terminal-page\"\n        :event-bus=\"searchEventBus\"\n        :search-params=\"searchForm\"\n        :newcolumn=\"columns\"\n        @datas=\"getDatas\"\n        @columnChange=\"getColumn\"\n        ref=\"grid\"\n      >\n        <div slot=\"search\">\n          <el-form\n            :inline=\"true\"\n            :model=\"searchForm\"\n            class=\"demo-form-inline\"\n          >\n            <el-form-item label=\"终端ID\">\n              <el-input\n                style=\"width: 200px;margin:0 10px 0 0;\"\n                v-model.trim=\"searchForm.terminalId\"\n                size=\"small\"\n                placeholder=\"请输入终端ID\"\n              ></el-input>\n            </el-form-item>\n            <el-form-item label=\"终端IP\">\n              <el-input\n                style=\"width: 200px;margin:0 10px 0 0;\"\n                v-model.trim=\"searchForm.terminalIp\"\n                size=\"small\"\n                placeholder=\"请输入终端IP\"\n              ></el-input>\n            </el-form-item>\n            <el-form-item label=\"终端名称\">\n              <el-input\n                style=\"width: 200px;margin:0 10px 0 0;\"\n                v-model.trim=\"searchForm.terminalName\"\n                size=\"small\"\n                placeholder=\"请输入终端名称\"\n              ></el-input>\n            </el-form-item>\n            <el-form-item label=\"状态\">\n              <el-select\n                size=\"small\"\n                clearable\n                @keydown.enter.native.prevent=\"searchTable\"\n                v-model=\"searchForm.status\"\n                placeholder=\"请选择状态\"\n              >\n                <el-option label=\"在线\" value=\"online\"></el-option>\n                <el-option label=\"离线\" value=\"offline\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"分配状态\">\n              <el-select\n                size=\"small\"\n                clearable\n                @keydown.enter.native.prevent=\"searchTable\"\n                v-model=\"searchForm.assignStatus\"\n                placeholder=\"请选择分配状态\"\n              >\n                <el-option label=\"已分配\" value=\"assigned\"></el-option>\n                <el-option label=\"待分配\" value=\"pending\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item>\n              <el-button\n                size=\"small\"\n                type=\"primary\"\n                style=\"margin: 0 0 0 10px\"\n                @click=\"searchTable\"\n                >搜索</el-button>\n              <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n        <el-table slot=\"table\" slot-scope=\"{}\" v-loading=\"tableLoading\" ref=\"multipleTable\" :data=\"tableData\" stripe style=\"width: 100%\">\n          <el-table-column fixed=\"left\" :align=\"'center'\" label=\"序号\" type=\"index\" width=\"50\">\n          </el-table-column>\n          <template v-for=\"(item, index) in columns\">\n            <el-table-column\n              v-if=\"item.slot === 'status'\"\n              :show-overflow-tooltip=\"true\"\n              :align=\"item.align ? item.align : 'center'\"\n              :key=\"index\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              min-width=\"100\"\n            >\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"scope.row.status === 'online' ? 'success' : 'danger'\">\n                  {{ scope.row.status === 'online' ? '在线' : '离线' }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column\n              v-else-if=\"item.slot === 'assignStatus'\"\n              :show-overflow-tooltip=\"true\"\n              :align=\"item.align ? item.align : 'center'\"\n              :key=\"index\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              min-width=\"100\"\n            >\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"scope.row.assignStatus === 'assigned' ? 'success' : 'warning'\">\n                  {{ scope.row.assignStatus === 'assigned' ? '已分配' : '待分配' }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column\n              v-else\n              :show-overflow-tooltip=\"true\"\n              :key=\"item.key\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              :min-width=\"item.width ? item.width : '150'\"\n              :align=\"item.align ? item.align : 'center'\"\n            >\n            </el-table-column>\n          </template>\n          <el-table-column\n            fixed=\"right\"\n            align=\"center\"\n            label=\"操作\"\n            type=\"action\"\n            width=\"200\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                v-if=\"scope.row.assignStatus === 'assigned'\"\n                style=\"margin-right:6px\"\n                @click=\"handleChangeTenant(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                >更改租户</el-button>\n              <el-button\n                v-if=\"scope.row.assignStatus === 'pending'\"\n                style=\"margin-right:6px\"\n                @click=\"handleAssignTenant(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                >分配租户</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </Grid>\n    </el-col>\n  </el-row>\n\n  <!-- 分配租户弹窗 -->\n  <el-dialog\n    :title=\"dialogTitle\"\n    :visible.sync=\"assignDialogVisible\"\n    width=\"500px\"\n    :close-on-click-modal=\"false\"\n  >\n    <el-form :model=\"assignForm\" :rules=\"assignRules\" ref=\"assignForm\" label-width=\"100px\">\n      <el-form-item label=\"终端ID\">\n        <el-input v-model=\"assignForm.terminalId\" disabled></el-input>\n      </el-form-item>\n      <el-form-item label=\"终端名称\">\n        <el-input v-model=\"assignForm.terminalName\" disabled></el-input>\n      </el-form-item>\n      <el-form-item label=\"选择租户\" prop=\"tenantId\">\n        <el-select v-model=\"assignForm.tenantId\" placeholder=\"请选择租户\" style=\"width: 100%\">\n          <el-option\n            v-for=\"tenant in tenantList\"\n            :key=\"tenant.id\"\n            :label=\"tenant.tenantName\"\n            :value=\"tenant.id\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n    </el-form>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"assignDialogVisible = false\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"confirmAssign\" :loading=\"submitLoading\">确 定</el-button>\n    </div>\n  </el-dialog>\n</div>\n", null]}