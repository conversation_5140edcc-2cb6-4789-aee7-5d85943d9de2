{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\tenant\\index.vue", "mtime": 1753840860474}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import \"core-js/modules/web.dom.iterable\";\nimport Vue from 'vue';\nimport Grid from '@/components/Grid';\nimport TenantForm from \"./components/TenantForm.vue\";\nimport AdminManageDialog from \"./components/AdminManageDialog.vue\";\nimport LoadingFix from '@/utils/loading-fix';\nvar defaultSearchForm = {\n  tenantCode: '',\n  tenantName: '',\n  tenantAdmin: '',\n  isSelected: ''\n};\nvar defaultConfigForm = {\n  \"id\": 0,\n  \"tenantCode\": \"\",\n  \"tenantName\": \"\",\n  \"tenantAdmin\": \"\",\n  \"comments\": \"\",\n  \"isSelected\": false\n};\nexport default {\n  components: {\n    Grid: Grid,\n    TenantForm: TenantForm,\n    AdminManageDialog: AdminManageDialog\n  },\n  destroyed: function destroyed() {\n    this.searchEventBus.$off();\n    this.searchConfigEventBus.$off();\n\n    // 清理遮罩层观察器\n    if (this.maskObserver) {\n      this.maskObserver.disconnect();\n    }\n  },\n  data: function data() {\n    this.searchEventBus = new Vue();\n    this.searchConfigEventBus = new Vue();\n    return {\n      configDrawer: false,\n      configDrawerName: '添加',\n      configStatus: true,\n      tenantRuleForm: Object.assign({}, defaultConfigForm),\n      isEditMode: false,\n      submitLoading: false,\n      tableLoading: false,\n      configTableData: [],\n      itemId: null,\n      configDialogName: '',\n      configDialog: false,\n      status: true,\n      rules: {\n        dictName: [{\n          required: true,\n          message: '请输入字典名称',\n          trigger: 'change,blur'\n        }],\n        dictCode: [{\n          required: true,\n          message: '请输入字典代码',\n          trigger: 'change,blur'\n        }],\n        dictOrder: [{\n          required: true,\n          message: '请输入字典排序',\n          trigger: 'change,blur'\n        }]\n      },\n      drawerName: '添加',\n      drawer: false,\n      direction: 'rtl',\n      searchForm: Object.assign({}, defaultSearchForm),\n      columns: [{\n        title: '租户编码',\n        key: 'tenantCode',\n        tooltip: true,\n        minWidth: 130\n      }, {\n        title: '租户名称',\n        key: 'tenantName',\n        tooltip: true,\n        minWidth: 130\n      }, {\n        title: '租户管理员',\n        key: 'tenantAdmin',\n        tooltip: true,\n        minWidth: 150\n      }, {\n        title: '备注',\n        key: 'comments',\n        tooltip: true,\n        minWidth: 200\n      }, {\n        title: '是否选中',\n        slot: 'isSelected',\n        tooltip: true,\n        minWidth: 100\n      }],\n      tableData: [],\n      // 维护管理员相关\n      adminManageVisible: false,\n      currentTenant: {}\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n    // 确保页面加载时重置所有loading状态\n    this.tableLoading = false;\n\n    // 使用 LoadingFix 工具清理遮罩层\n    this.$nextTick(function () {\n      setTimeout(function () {\n        LoadingFix.forceClearMasks();\n        _this.tableLoading = false;\n        console.log('已使用 LoadingFix 重置页面loading状态');\n      }, 100);\n    });\n\n    // 启动自动清理监听\n    this.maskObserver = LoadingFix.startAutoCleanup();\n  },\n  methods: {\n    // 获取数据时的回调\n    getDatas: function getDatas(data) {\n      this.tableData = data;\n      this.tableLoading = false; // 确保数据加载完成后隐藏loading\n    },\n    handleSelectionChange: function handleSelectionChange(row) {\n      // 处理选中状态变化\n      console.log('选中状态变化:', row.tenantName, row.isSelected);\n      // 这里可以添加API调用来保存选中状态\n      // this.$api['tenant/update-selection']({id: row.id, isSelected: row.isSelected})\n    },\n    handleAdd: function handleAdd() {\n      this.drawer = true;\n      // 使用浅拷贝替代深拷贝，提高性能\n      this.tenantRuleForm = Object.assign({}, defaultConfigForm);\n      this.drawerName = '新建租户';\n      this.isEditMode = false;\n    },\n    handleEdit: function handleEdit(row) {\n      this.tenantRuleForm = Object.assign({}, row);\n      this.drawer = true;\n      this.drawerName = '编辑租户';\n      this.isEditMode = true;\n    },\n    handleAdminManage: function handleAdminManage(row) {\n      this.currentTenant = Object.assign({}, row);\n      this.adminManageVisible = true;\n    },\n    batchDelete: function batchDelete() {\n      var _this2 = this;\n      var ids = [];\n      this.$refs.multipleTable.selection.forEach(function (e) {\n        ids.push(e.id);\n      });\n      console.info(ids, this.$refs.multipleTable.selection);\n      if (!ids.length) {\n        this.$message.info('请先选择您要删除的项');\n        return;\n      }\n      this.$confirm('此操作将删除租户信息?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        _this2.$api['tenant/tenant-delete']({\n          ids: ids\n        }).then(function (data) {\n          _this2.$refs.grid.query();\n          _this2.$message({\n            message: '删除成功',\n            type: 'success'\n          });\n        });\n      }).catch(function (err) {});\n    },\n    handleDelete: function handleDelete(e) {\n      var _this3 = this;\n      this.$api['tenant/tenant-delete']({\n        ids: [e]\n      }).then(function (data) {\n        _this3.$refs.grid.query();\n        _this3.$message({\n          message: '删除成功',\n          type: 'success'\n        });\n      });\n    },\n    searchTable: function searchTable() {\n      this.$refs.grid.query();\n    },\n    resetTable: function resetTable() {\n      var _this4 = this;\n      // 使用浅拷贝替代深拷贝，提高性能\n      this.searchForm = Object.assign({}, defaultSearchForm);\n      this.$nextTick(function () {\n        _this4.$refs.grid.query();\n      });\n    },\n    getColumn: function getColumn(e) {\n      this.columns = e;\n    },\n    handleConfigEdit: function handleConfigEdit(row) {\n      this.configStatus = !!row.dictStatus;\n      this.tenantRuleForm = Object.assign({}, row);\n      this.configDrawer = true;\n      this.configDrawerName = '编辑';\n    },\n    handleConfigDelete: function handleConfigDelete(e) {\n      var _this5 = this;\n      this.$api['sysDict/dict-item-delete']({\n        ids: [e]\n      }).then(function (data) {\n        _this5.$refs.configGrid.query();\n        _this5.$message({\n          message: '删除成功',\n          type: 'success'\n        });\n      });\n    },\n    // 表单提交处理\n    handleFormSubmit: function handleFormSubmit(formData) {\n      var _this6 = this;\n      this.submitLoading = true;\n      this.$api['tenant/tenant-save'](formData).then(function (data) {\n        _this6.$message({\n          message: '保存成功',\n          type: 'success'\n        });\n        _this6.drawer = false;\n        _this6.$refs.grid.query();\n        _this6.resetFormData();\n      }).catch(function (error) {\n        console.error('保存失败:', error);\n      }).finally(function () {\n        _this6.submitLoading = false;\n      });\n    },\n    // 表单取消处理\n    handleFormCancel: function handleFormCancel() {\n      this.drawer = false;\n      this.resetFormData();\n    },\n    // 重置表单数据\n    resetFormData: function resetFormData() {\n      // 使用浅拷贝替代深拷贝，提高性能\n      this.tenantRuleForm = Object.assign({}, defaultConfigForm);\n      this.isEditMode = false;\n      this.submitLoading = false;\n    }\n  }\n};", {"version": 3, "names": ["<PERSON><PERSON>", "Grid", "TenantForm", "AdminManageDialog", "LoadingFix", "defaultSearchForm", "tenantCode", "tenantName", "tenantAdmin", "isSelected", "defaultConfigForm", "components", "destroyed", "searchEventBus", "$off", "searchConfigEventBus", "maskObserver", "disconnect", "data", "config<PERSON><PERSON><PERSON>", "configDrawerName", "config<PERSON><PERSON>us", "tenantRuleForm", "Object", "assign", "isEditMode", "submitLoading", "tableLoading", "configTableData", "itemId", "configDialogName", "config<PERSON><PERSON><PERSON>", "status", "rules", "dictName", "required", "message", "trigger", "dictCode", "dictOrder", "drawerName", "drawer", "direction", "searchForm", "columns", "title", "key", "tooltip", "min<PERSON><PERSON><PERSON>", "slot", "tableData", "adminManageVisible", "currentTenant", "mounted", "_this", "$nextTick", "setTimeout", "forceClearMasks", "console", "log", "startAutoCleanup", "methods", "getDatas", "handleSelectionChange", "row", "handleAdd", "handleEdit", "handleAdminManage", "batchDelete", "_this2", "ids", "$refs", "multipleTable", "selection", "for<PERSON>ach", "e", "push", "id", "info", "length", "$message", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "$api", "grid", "query", "catch", "err", "handleDelete", "_this3", "searchTable", "resetTable", "_this4", "getColumn", "handleConfigEdit", "dictStatus", "handleConfigDelete", "_this5", "config<PERSON><PERSON>", "handleFormSubmit", "formData", "_this6", "resetFormData", "error", "finally", "handleFormCancel"], "sources": ["src/bysc_system/views/terminalAssignment/index.vue"], "sourcesContent": ["\r\n<template>\r\n  <div>\r\n    <el-row>\r\n      <el-col :span=\"24\">\r\n        <Grid\r\n          api=\"tenant/tenant-page\"\r\n          :event-bus=\"searchEventBus\"\r\n          :search-params=\"searchForm\"\r\n          :newcolumn=\"columns\"\r\n          @datas=\"getDatas\"\r\n          @columnChange=\"getColumn\"\r\n          ref=\"grid\"\r\n        >\r\n          <div slot=\"search\">\r\n            <el-form\r\n              :inline=\"true\"\r\n              :model=\"searchForm\"\r\n              class=\"demo-form-inline\"\r\n            >\r\n              <el-form-item label=\"租户编码\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.tenantCode\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入租户编码\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"租户名称\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.tenantName\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入租户名称\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"租户管理员\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.tenantAdmin\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入租户管理员\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"是否选中\">\r\n                <el-select\r\n                  size=\"small\"\r\n                  clearable\r\n                  @keydown.enter.native.prevent=\"searchTable\"\r\n                  v-model=\"searchForm.isSelected\"\r\n                  placeholder=\"请选择是否选中\"\r\n                >\r\n                  <el-option :label=\"'是'\" :value=\"true\"> </el-option>\r\n                  <el-option :label=\"'否'\" :value=\"false\"> </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"primary\"\r\n                  style=\"margin: 0 0 0 10px\"\r\n                  @click=\"searchTable\"\r\n                  >搜索</el-button>\r\n                <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n          <div slot=\"action\">\r\n            <el-button v-permission=\"'tenant_add'\" size=\"small\" type=\"primary\" @click=\"handleAdd\">新建租户</el-button>\r\n            <el-button v-permission=\"'tenant_batchDel'\" size=\"small\" @click=\"batchDelete\">删除租户</el-button>\r\n          </div>\r\n          <el-table slot=\"table\" slot-scope=\"{}\" v-loading=\"tableLoading\" ref=\"multipleTable\" :data=\"tableData\" stripe style=\"width: 100%\">\r\n            <el-table-column fixed=\"left\" :align=\"'center'\" type=\"selection\" width=\"55\">\r\n            </el-table-column>\r\n            <el-table-column fixed=\"left\" :align=\"'center'\" label=\"序号\" type=\"index\" width=\"50\">\r\n            </el-table-column>\r\n            <template v-for=\"(item, index) in columns\">\r\n              <el-table-column\r\n                v-if=\"item.slot === 'isSelected'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"100\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-switch\r\n                    v-model=\"scope.row.isSelected\"\r\n                    @change=\"handleSelectionChange(scope.row)\"\r\n                    active-text=\"是\"\r\n                    inactive-text=\"否\">\r\n                  </el-switch>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else-if=\"item.slot\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"180\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"scope.row[item.slot]?'success':'danger'\">{{scope.row[item.slot]?'启用':'禁用'}}</el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else\r\n                :show-overflow-tooltip=\"true\"\r\n                :key=\"item.key\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                :min-width=\"item.width ? item.width : '150'\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n              >\r\n              </el-table-column>\r\n            </template>\r\n            <el-table-column\r\n              fixed=\"right\"\r\n              align=\"center\"\r\n              label=\"操作\"\r\n              type=\"action\"\r\n              width=\"200\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <template>\r\n                  <el-button\r\n                    v-permission=\"'tenant_edit'\"\r\n                    style=\"margin-right:6px\"\r\n                    @click=\"handleEdit(scope.row)\"\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    >编辑</el-button\r\n                  >\r\n                </template>\r\n\r\n                <template>\r\n\r\n                  <el-button\r\n                    v-permission=\"'tenant_admin_manage'\"\r\n                    style=\"margin-right:6px\"\r\n                    @click=\"handleAdminManage(scope.row)\"\r\n                    type=\"text\"\r\n                    size=\"small\"\r\n                    >维护管理员</el-button\r\n                  >\r\n                </template>\r\n\r\n                <template>\r\n                  <el-popconfirm\r\n                  @confirm=\"handleDelete(scope.row.id)\"\r\n                    title=\"您确定要删除该租户吗？\">\r\n                    <el-button v-permission=\"'tenant_del'\" type=\"text\" size=\"small\" slot=\"reference\">删除</el-button>\r\n                  </el-popconfirm>\r\n                </template>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </Grid>\r\n      </el-col>\r\n    </el-row>\r\n    <el-drawer\r\n      size=\"50%\"\r\n      :title=\"drawerName\"\r\n      :visible.sync=\"drawer\"\r\n      :direction=\"direction\"\r\n      :wrapperClosable=\"false\"\r\n    >\r\n      <TenantForm\r\n        v-model=\"tenantRuleForm\"\r\n        :is-edit=\"isEditMode\"\r\n        :loading=\"submitLoading\"\r\n        @submit=\"handleFormSubmit\"\r\n        @cancel=\"handleFormCancel\"\r\n        ref=\"tenantForm\"\r\n      />\r\n    </el-drawer>\r\n\r\n    <!-- 维护管理员弹窗 -->\r\n    <AdminManageDialog\r\n      :visible.sync=\"adminManageVisible\"\r\n      :tenant-info=\"currentTenant\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport Grid from '@/components/Grid';\r\nimport TenantForm from './components/TenantForm.vue';\r\nimport AdminManageDialog from './components/AdminManageDialog.vue';\r\nimport LoadingFix from '@/utils/loading-fix';\r\nconst defaultSearchForm = {\r\n  tenantCode: '',\r\n  tenantName: '',\r\n  tenantAdmin: '',\r\n  isSelected: ''\r\n};\r\nconst defaultConfigForm = {\r\n  \"id\": 0,\r\n  \"tenantCode\": \"\",\r\n  \"tenantName\": \"\",\r\n  \"tenantAdmin\": \"\",\r\n  \"comments\": \"\",\r\n  \"isSelected\": false\r\n};\r\nexport default {\r\n  components: {Grid, TenantForm, AdminManageDialog},\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n    this.searchConfigEventBus.$off();\r\n\r\n    // 清理遮罩层观察器\r\n    if (this.maskObserver) {\r\n      this.maskObserver.disconnect();\r\n    }\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    this.searchConfigEventBus = new Vue();\r\n    return {\r\n      configDrawer: false,\r\n      configDrawerName: '添加',\r\n      configStatus: true,\r\n      tenantRuleForm: Object.assign({}, defaultConfigForm),\r\n      isEditMode: false,\r\n      submitLoading: false,\r\n      tableLoading: false,\r\n      configTableData: [],\r\n      itemId: null,\r\n      configDialogName: '',\r\n      configDialog: false,\r\n      status: true,\r\n      rules: {\r\n        dictName: [\r\n          {required: true, message: '请输入字典名称', trigger: 'change,blur'}\r\n        ],\r\n        dictCode: [\r\n          {required: true, message: '请输入字典代码', trigger: 'change,blur'}\r\n        ],\r\n        dictOrder: [\r\n          {required: true, message: '请输入字典排序', trigger: 'change,blur'},\r\n        ],\r\n      },\r\n      drawerName: '添加',\r\n      drawer: false,\r\n      direction: 'rtl',\r\n      searchForm: Object.assign({}, defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: '租户编码',\r\n          key: 'tenantCode',\r\n          tooltip: true,\r\n          minWidth: 130,\r\n        },\r\n        {\r\n          title: '租户名称',\r\n          key: 'tenantName',\r\n          tooltip: true,\r\n          minWidth: 130,\r\n        },\r\n        {\r\n          title: '租户管理员',\r\n          key: 'tenantAdmin',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '备注',\r\n          key: 'comments',\r\n          tooltip: true,\r\n          minWidth: 200,\r\n        },\r\n        {\r\n          title: '是否选中',\r\n          slot: 'isSelected',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n      ],\r\n      tableData: [],\r\n      // 维护管理员相关\r\n      adminManageVisible: false,\r\n      currentTenant: {}\r\n    };\r\n  },\r\n  mounted() {\r\n    // 确保页面加载时重置所有loading状态\r\n    this.tableLoading = false;\r\n\r\n    // 使用 LoadingFix 工具清理遮罩层\r\n    this.$nextTick(() => {\r\n      setTimeout(() => {\r\n        LoadingFix.forceClearMasks();\r\n        this.tableLoading = false;\r\n        console.log('已使用 LoadingFix 重置页面loading状态');\r\n      }, 100);\r\n    });\r\n\r\n    // 启动自动清理监听\r\n    this.maskObserver = LoadingFix.startAutoCleanup();\r\n  },\r\n\r\n  methods: {\r\n    // 获取数据时的回调\r\n    getDatas(data) {\r\n      this.tableData = data;\r\n      this.tableLoading = false; // 确保数据加载完成后隐藏loading\r\n    },\r\n\r\n    handleSelectionChange(row) {\r\n      // 处理选中状态变化\r\n      console.log('选中状态变化:', row.tenantName, row.isSelected);\r\n      // 这里可以添加API调用来保存选中状态\r\n      // this.$api['tenant/update-selection']({id: row.id, isSelected: row.isSelected})\r\n    },\r\n\r\n    handleAdd() {\r\n      this.drawer = true;\r\n      // 使用浅拷贝替代深拷贝，提高性能\r\n      this.tenantRuleForm = Object.assign({}, defaultConfigForm);\r\n      this.drawerName = '新建租户';\r\n      this.isEditMode = false;\r\n    },\r\n    handleEdit(row) {\r\n      this.tenantRuleForm = Object.assign({}, row);\r\n      this.drawer = true;\r\n      this.drawerName = '编辑租户';\r\n      this.isEditMode = true;\r\n    },\r\n    handleAdminManage(row) {\r\n      this.currentTenant = Object.assign({}, row);\r\n      this.adminManageVisible = true;\r\n    },\r\n    batchDelete() {\r\n      let ids = [];\r\n      this.$refs.multipleTable.selection.forEach(e => {\r\n        ids.push(e.id);\r\n      });\r\n      console.info(ids, this.$refs.multipleTable.selection);\r\n      if (!ids.length) {\r\n        this.$message.info('请先选择您要删除的项');\r\n        return;\r\n      }\r\n      this.$confirm('此操作将删除租户信息?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$api['tenant/tenant-delete']({ids: ids}).then(data => {\r\n          this.$refs.grid.query();\r\n          this.$message({\r\n            message: '删除成功',\r\n            type: 'success'\r\n          });\r\n        });\r\n      }).catch(err => {});\r\n    },\r\n    handleDelete(e) {\r\n      this.$api['tenant/tenant-delete']({ids: [e]}).then(data => {\r\n        this.$refs.grid.query();\r\n        this.$message({\r\n          message: '删除成功',\r\n          type: 'success'\r\n        });\r\n      });\r\n    },\r\n\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n    resetTable() {\r\n      // 使用浅拷贝替代深拷贝，提高性能\r\n      this.searchForm = Object.assign({}, defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n    getColumn(e) {\r\n      this.columns = e;\r\n    },\r\n    handleConfigEdit(row) {\r\n      this.configStatus = !!row.dictStatus;\r\n      this.tenantRuleForm = Object.assign({}, row);\r\n      this.configDrawer = true;\r\n      this.configDrawerName = '编辑';\r\n    },\r\n    handleConfigDelete(e) {\r\n      this.$api['sysDict/dict-item-delete']({ids: [e]}).then(data => {\r\n        this.$refs.configGrid.query();\r\n        this.$message({\r\n          message: '删除成功',\r\n          type: 'success'\r\n        });\r\n      });\r\n    },\r\n    // 表单提交处理\r\n    handleFormSubmit(formData) {\r\n      this.submitLoading = true;\r\n      this.$api['tenant/tenant-save'](formData).then(data => {\r\n        this.$message({\r\n          message: '保存成功',\r\n          type: 'success'\r\n        });\r\n        this.drawer = false;\r\n        this.$refs.grid.query();\r\n        this.resetFormData();\r\n      }).catch(error => {\r\n        console.error('保存失败:', error);\r\n      }).finally(() => {\r\n        this.submitLoading = false;\r\n      });\r\n    },\r\n\r\n    // 表单取消处理\r\n    handleFormCancel() {\r\n      this.drawer = false;\r\n      this.resetFormData();\r\n    },\r\n\r\n    // 重置表单数据\r\n    resetFormData() {\r\n      // 使用浅拷贝替代深拷贝，提高性能\r\n      this.tenantRuleForm = Object.assign({}, defaultConfigForm);\r\n      this.isEditMode = false;\r\n      this.submitLoading = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped></style>\r\n"], "mappings": ";AA6LA,OAAAA,GAAA;AACA,OAAAC,IAAA;AACA,OAAAC,UAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,UAAA;AACA,IAAAC,iBAAA;EACAC,UAAA;EACAC,UAAA;EACAC,WAAA;EACAC,UAAA;AACA;AACA,IAAAC,iBAAA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;AACA;EACAC,UAAA;IAAAV,IAAA,EAAAA,IAAA;IAAAC,UAAA,EAAAA,UAAA;IAAAC,iBAAA,EAAAA;EAAA;EACAS,SAAA,WAAAA,UAAA;IACA,KAAAC,cAAA,CAAAC,IAAA;IACA,KAAAC,oBAAA,CAAAD,IAAA;;IAEA;IACA,SAAAE,YAAA;MACA,KAAAA,YAAA,CAAAC,UAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA,KAAAL,cAAA,OAAAb,GAAA;IACA,KAAAe,oBAAA,OAAAf,GAAA;IACA;MACAmB,YAAA;MACAC,gBAAA;MACAC,YAAA;MACAC,cAAA,EAAAC,MAAA,CAAAC,MAAA,KAAAd,iBAAA;MACAe,UAAA;MACAC,aAAA;MACAC,YAAA;MACAC,eAAA;MACAC,MAAA;MACAC,gBAAA;MACAC,YAAA;MACAC,MAAA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,SAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAG,UAAA;MACAC,MAAA;MACAC,SAAA;MACAC,UAAA,EAAApB,MAAA,CAAAC,MAAA,KAAAnB,iBAAA;MACAuC,OAAA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAI,IAAA;QACAF,OAAA;QACAC,QAAA;MACA,EACA;MACAE,SAAA;MACA;MACAC,kBAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,KAAA3B,YAAA;;IAEA;IACA,KAAA4B,SAAA;MACAC,UAAA;QACApD,UAAA,CAAAqD,eAAA;QACAH,KAAA,CAAA3B,YAAA;QACA+B,OAAA,CAAAC,GAAA;MACA;IACA;;IAEA;IACA,KAAA3C,YAAA,GAAAZ,UAAA,CAAAwD,gBAAA;EACA;EAEAC,OAAA;IACA;IACAC,QAAA,WAAAA,SAAA5C,IAAA;MACA,KAAAgC,SAAA,GAAAhC,IAAA;MACA,KAAAS,YAAA;IACA;IAEAoC,qBAAA,WAAAA,sBAAAC,GAAA;MACA;MACAN,OAAA,CAAAC,GAAA,YAAAK,GAAA,CAAAzD,UAAA,EAAAyD,GAAA,CAAAvD,UAAA;MACA;MACA;IACA;IAEAwD,SAAA,WAAAA,UAAA;MACA,KAAAxB,MAAA;MACA;MACA,KAAAnB,cAAA,GAAAC,MAAA,CAAAC,MAAA,KAAAd,iBAAA;MACA,KAAA8B,UAAA;MACA,KAAAf,UAAA;IACA;IACAyC,UAAA,WAAAA,WAAAF,GAAA;MACA,KAAA1C,cAAA,GAAAC,MAAA,CAAAC,MAAA,KAAAwC,GAAA;MACA,KAAAvB,MAAA;MACA,KAAAD,UAAA;MACA,KAAAf,UAAA;IACA;IACA0C,iBAAA,WAAAA,kBAAAH,GAAA;MACA,KAAAZ,aAAA,GAAA7B,MAAA,CAAAC,MAAA,KAAAwC,GAAA;MACA,KAAAb,kBAAA;IACA;IACAiB,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,GAAA;MACA,KAAAC,KAAA,CAAAC,aAAA,CAAAC,SAAA,CAAAC,OAAA,WAAAC,CAAA;QACAL,GAAA,CAAAM,IAAA,CAAAD,CAAA,CAAAE,EAAA;MACA;MACAnB,OAAA,CAAAoB,IAAA,CAAAR,GAAA,OAAAC,KAAA,CAAAC,aAAA,CAAAC,SAAA;MACA,KAAAH,GAAA,CAAAS,MAAA;QACA,KAAAC,QAAA,CAAAF,IAAA;QACA;MACA;MACA,KAAAG,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACAhB,MAAA,CAAAiB,IAAA;UAAAhB,GAAA,EAAAA;QAAA,GAAAe,IAAA,WAAAnE,IAAA;UACAmD,MAAA,CAAAE,KAAA,CAAAgB,IAAA,CAAAC,KAAA;UACAnB,MAAA,CAAAW,QAAA;YACA5C,OAAA;YACAgD,IAAA;UACA;QACA;MACA,GAAAK,KAAA,WAAAC,GAAA;IACA;IACAC,YAAA,WAAAA,aAAAhB,CAAA;MAAA,IAAAiB,MAAA;MACA,KAAAN,IAAA;QAAAhB,GAAA,GAAAK,CAAA;MAAA,GAAAU,IAAA,WAAAnE,IAAA;QACA0E,MAAA,CAAArB,KAAA,CAAAgB,IAAA,CAAAC,KAAA;QACAI,MAAA,CAAAZ,QAAA;UACA5C,OAAA;UACAgD,IAAA;QACA;MACA;IACA;IAEAS,WAAA,WAAAA,YAAA;MACA,KAAAtB,KAAA,CAAAgB,IAAA,CAAAC,KAAA;IACA;IACAM,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAApD,UAAA,GAAApB,MAAA,CAAAC,MAAA,KAAAnB,iBAAA;MACA,KAAAkD,SAAA;QACAwC,MAAA,CAAAxB,KAAA,CAAAgB,IAAA,CAAAC,KAAA;MACA;IACA;IACAQ,SAAA,WAAAA,UAAArB,CAAA;MACA,KAAA/B,OAAA,GAAA+B,CAAA;IACA;IACAsB,gBAAA,WAAAA,iBAAAjC,GAAA;MACA,KAAA3C,YAAA,KAAA2C,GAAA,CAAAkC,UAAA;MACA,KAAA5E,cAAA,GAAAC,MAAA,CAAAC,MAAA,KAAAwC,GAAA;MACA,KAAA7C,YAAA;MACA,KAAAC,gBAAA;IACA;IACA+E,kBAAA,WAAAA,mBAAAxB,CAAA;MAAA,IAAAyB,MAAA;MACA,KAAAd,IAAA;QAAAhB,GAAA,GAAAK,CAAA;MAAA,GAAAU,IAAA,WAAAnE,IAAA;QACAkF,MAAA,CAAA7B,KAAA,CAAA8B,UAAA,CAAAb,KAAA;QACAY,MAAA,CAAApB,QAAA;UACA5C,OAAA;UACAgD,IAAA;QACA;MACA;IACA;IACA;IACAkB,gBAAA,WAAAA,iBAAAC,QAAA;MAAA,IAAAC,MAAA;MACA,KAAA9E,aAAA;MACA,KAAA4D,IAAA,uBAAAiB,QAAA,EAAAlB,IAAA,WAAAnE,IAAA;QACAsF,MAAA,CAAAxB,QAAA;UACA5C,OAAA;UACAgD,IAAA;QACA;QACAoB,MAAA,CAAA/D,MAAA;QACA+D,MAAA,CAAAjC,KAAA,CAAAgB,IAAA,CAAAC,KAAA;QACAgB,MAAA,CAAAC,aAAA;MACA,GAAAhB,KAAA,WAAAiB,KAAA;QACAhD,OAAA,CAAAgD,KAAA,UAAAA,KAAA;MACA,GAAAC,OAAA;QACAH,MAAA,CAAA9E,aAAA;MACA;IACA;IAEA;IACAkF,gBAAA,WAAAA,iBAAA;MACA,KAAAnE,MAAA;MACA,KAAAgE,aAAA;IACA;IAEA;IACAA,aAAA,WAAAA,cAAA;MACA;MACA,KAAAnF,cAAA,GAAAC,MAAA,CAAAC,MAAA,KAAAd,iBAAA;MACA,KAAAe,UAAA;MACA,KAAAC,aAAA;IACA;EACA;AACA", "ignoreList": []}]}