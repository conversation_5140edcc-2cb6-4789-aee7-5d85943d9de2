{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\index.vue", "mtime": 1753841087327}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\r\nimport Vue from 'vue';\r\nimport Grid from '@/components/Grid';\r\nimport LoadingFix from '@/utils/loading-fix';\r\n\r\nconst defaultSearchForm = {\r\n  terminalId: '',\r\n  terminalIp: '',\r\n  terminalName: '',\r\n  status: '',\r\n  assignStatus: ''\r\n};\r\n\r\nconst defaultAssignForm = {\r\n  terminalId: '',\r\n  terminalName: '',\r\n  tenantId: ''\r\n};\r\n\r\nexport default {\r\n  components: {Grid},\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n\r\n    // 清理遮罩层观察器\r\n    if (this.maskObserver) {\r\n      this.maskObserver.disconnect();\r\n    }\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    return {\r\n      submitLoading: false,\r\n      tableLoading: false,\r\n      searchForm: Object.assign({}, defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: '终端ID',\r\n          key: 'terminalId',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '终端IP',\r\n          key: 'terminalIp',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '终端名称',\r\n          key: 'terminalName',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '终端上线时间',\r\n          key: 'onlineTime',\r\n          tooltip: true,\r\n          minWidth: 180,\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '分配状态',\r\n          key: 'assignStatus',\r\n          slot: 'assignStatus',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '当前租户',\r\n          key: 'currentTenant',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n      ],\r\n      tableData: [],\r\n      // 分配租户相关\r\n      assignDialogVisible: false,\r\n      dialogTitle: '',\r\n      assignForm: Object.assign({}, defaultAssignForm),\r\n      assignRules: {\r\n        tenantId: [\r\n          {required: true, message: '请选择租户', trigger: 'change'}\r\n        ]\r\n      },\r\n      tenantList: [], // 租户列表\r\n      currentTerminal: {} // 当前操作的终端\r\n    };\r\n  },\r\n  mounted() {\r\n    // 确保页面加载时重置所有loading状态\r\n    this.tableLoading = false;\r\n\r\n    // 使用 LoadingFix 工具清理遮罩层\r\n    this.$nextTick(() => {\r\n      setTimeout(() => {\r\n        LoadingFix.forceClearMasks();\r\n        this.tableLoading = false;\r\n        console.log('已使用 LoadingFix 重置页面loading状态');\r\n      }, 100);\r\n    });\r\n\r\n    // 启动自动清理监听\r\n    this.maskObserver = LoadingFix.startAutoCleanup();\r\n  },\r\n\r\n  methods: {\r\n    // 获取数据时的回调\r\n    getDatas(data) {\r\n      this.tableData = data;\r\n      this.tableLoading = false; // 确保数据加载完成后隐藏loading\r\n    },\r\n\r\n    // 分配租户\r\n    handleAssignTenant(row) {\r\n      this.currentTerminal = Object.assign({}, row);\r\n      this.assignForm = {\r\n        terminalId: row.terminalId,\r\n        terminalName: row.terminalName,\r\n        tenantId: ''\r\n      };\r\n      this.dialogTitle = '分配租户';\r\n      this.assignDialogVisible = true;\r\n      this.loadTenantList();\r\n    },\r\n\r\n    // 更改租户\r\n    handleChangeTenant(row) {\r\n      this.currentTerminal = Object.assign({}, row);\r\n      this.assignForm = {\r\n        terminalId: row.terminalId,\r\n        terminalName: row.terminalName,\r\n        tenantId: row.tenantId || ''\r\n      };\r\n      this.dialogTitle = '更改租户';\r\n      this.assignDialogVisible = true;\r\n      this.loadTenantList();\r\n    },\r\n\r\n    // 加载租户列表\r\n    loadTenantList() {\r\n      this.$api['tenant/tenant-list']().then(res => {\r\n        this.tenantList = res.data || [];\r\n      }).catch(error => {\r\n        console.error('加载租户列表失败:', error);\r\n        this.tenantList = [];\r\n      });\r\n    },\r\n\r\n    // 确认分配\r\n    confirmAssign() {\r\n      this.$refs.assignForm.validate(valid => {\r\n        if (valid) {\r\n          this.submitLoading = true;\r\n          const apiData = {\r\n            terminalId: this.assignForm.terminalId,\r\n            tenantId: this.assignForm.tenantId\r\n          };\r\n\r\n          this.$api['terminal/assign-tenant'](apiData).then(() => {\r\n            this.$message({\r\n              message: '分配成功',\r\n              type: 'success'\r\n            });\r\n            this.assignDialogVisible = false;\r\n            this.$refs.grid.query();\r\n          }).catch(error => {\r\n            console.error('分配失败:', error);\r\n          }).finally(() => {\r\n            this.submitLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n\r\n    resetTable() {\r\n      this.searchForm = Object.assign({}, defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n\r\n    getColumn(e) {\r\n      this.columns = e;\r\n    }\r\n  },\r\n};\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA2LA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/bysc_system/views/terminalAssignment", "sourcesContent": ["\r\n<template>\r\n  <div>\r\n    <el-row>\r\n      <el-col :span=\"24\">\r\n        <Grid\r\n          api=\"terminal/terminal-page\"\r\n          :event-bus=\"searchEventBus\"\r\n          :search-params=\"searchForm\"\r\n          :newcolumn=\"columns\"\r\n          @datas=\"getDatas\"\r\n          @columnChange=\"getColumn\"\r\n          ref=\"grid\"\r\n        >\r\n          <div slot=\"search\">\r\n            <el-form\r\n              :inline=\"true\"\r\n              :model=\"searchForm\"\r\n              class=\"demo-form-inline\"\r\n            >\r\n              <el-form-item label=\"终端ID\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.terminalId\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入终端ID\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"终端IP\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.terminalIp\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入终端IP\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"终端名称\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.terminalName\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入终端名称\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"状态\">\r\n                <el-select\r\n                  size=\"small\"\r\n                  clearable\r\n                  @keydown.enter.native.prevent=\"searchTable\"\r\n                  v-model=\"searchForm.status\"\r\n                  placeholder=\"请选择状态\"\r\n                >\r\n                  <el-option label=\"在线\" value=\"online\"></el-option>\r\n                  <el-option label=\"离线\" value=\"offline\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item label=\"分配状态\">\r\n                <el-select\r\n                  size=\"small\"\r\n                  clearable\r\n                  @keydown.enter.native.prevent=\"searchTable\"\r\n                  v-model=\"searchForm.assignStatus\"\r\n                  placeholder=\"请选择分配状态\"\r\n                >\r\n                  <el-option label=\"已分配\" value=\"assigned\"></el-option>\r\n                  <el-option label=\"待分配\" value=\"pending\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"primary\"\r\n                  style=\"margin: 0 0 0 10px\"\r\n                  @click=\"searchTable\"\r\n                  >搜索</el-button>\r\n                <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n          <el-table slot=\"table\" slot-scope=\"{}\" v-loading=\"tableLoading\" ref=\"multipleTable\" :data=\"tableData\" stripe style=\"width: 100%\">\r\n            <el-table-column fixed=\"left\" :align=\"'center'\" label=\"序号\" type=\"index\" width=\"50\">\r\n            </el-table-column>\r\n            <template v-for=\"(item, index) in columns\">\r\n              <el-table-column\r\n                v-if=\"item.slot === 'status'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"100\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"scope.row.status === 'online' ? 'success' : 'danger'\">\r\n                    {{ scope.row.status === 'online' ? '在线' : '离线' }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else-if=\"item.slot === 'assignStatus'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"100\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"scope.row.assignStatus === 'assigned' ? 'success' : 'warning'\">\r\n                    {{ scope.row.assignStatus === 'assigned' ? '已分配' : '待分配' }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else\r\n                :show-overflow-tooltip=\"true\"\r\n                :key=\"item.key\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                :min-width=\"item.width ? item.width : '150'\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n              >\r\n              </el-table-column>\r\n            </template>\r\n            <el-table-column\r\n              fixed=\"right\"\r\n              align=\"center\"\r\n              label=\"操作\"\r\n              type=\"action\"\r\n              width=\"200\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"scope.row.assignStatus === 'assigned'\"\r\n                  style=\"margin-right:6px\"\r\n                  @click=\"handleChangeTenant(scope.row)\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  >更改租户</el-button>\r\n                <el-button\r\n                  v-if=\"scope.row.assignStatus === 'pending'\"\r\n                  style=\"margin-right:6px\"\r\n                  @click=\"handleAssignTenant(scope.row)\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  >分配租户</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </Grid>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 分配租户弹窗 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"assignDialogVisible\"\r\n      width=\"500px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-form :model=\"assignForm\" :rules=\"assignRules\" ref=\"assignForm\" label-width=\"100px\">\r\n        <el-form-item label=\"终端ID\">\r\n          <el-input v-model=\"assignForm.terminalId\" disabled></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"终端名称\">\r\n          <el-input v-model=\"assignForm.terminalName\" disabled></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"选择租户\" prop=\"tenantId\">\r\n          <el-select v-model=\"assignForm.tenantId\" placeholder=\"请选择租户\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"tenant in tenantList\"\r\n              :key=\"tenant.id\"\r\n              :label=\"tenant.tenantName\"\r\n              :value=\"tenant.id\">\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"assignDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmAssign\" :loading=\"submitLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport Grid from '@/components/Grid';\r\nimport LoadingFix from '@/utils/loading-fix';\r\n\r\nconst defaultSearchForm = {\r\n  terminalId: '',\r\n  terminalIp: '',\r\n  terminalName: '',\r\n  status: '',\r\n  assignStatus: ''\r\n};\r\n\r\nconst defaultAssignForm = {\r\n  terminalId: '',\r\n  terminalName: '',\r\n  tenantId: ''\r\n};\r\n\r\nexport default {\r\n  components: {Grid},\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n\r\n    // 清理遮罩层观察器\r\n    if (this.maskObserver) {\r\n      this.maskObserver.disconnect();\r\n    }\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    return {\r\n      submitLoading: false,\r\n      tableLoading: false,\r\n      searchForm: Object.assign({}, defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: '终端ID',\r\n          key: 'terminalId',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: '终端IP',\r\n          key: 'terminalIp',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '终端名称',\r\n          key: 'terminalName',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '终端上线时间',\r\n          key: 'onlineTime',\r\n          tooltip: true,\r\n          minWidth: 180,\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '分配状态',\r\n          key: 'assignStatus',\r\n          slot: 'assignStatus',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '当前租户',\r\n          key: 'currentTenant',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n      ],\r\n      tableData: [],\r\n      // 分配租户相关\r\n      assignDialogVisible: false,\r\n      dialogTitle: '',\r\n      assignForm: Object.assign({}, defaultAssignForm),\r\n      assignRules: {\r\n        tenantId: [\r\n          {required: true, message: '请选择租户', trigger: 'change'}\r\n        ]\r\n      },\r\n      tenantList: [], // 租户列表\r\n      currentTerminal: {} // 当前操作的终端\r\n    };\r\n  },\r\n  mounted() {\r\n    // 确保页面加载时重置所有loading状态\r\n    this.tableLoading = false;\r\n\r\n    // 使用 LoadingFix 工具清理遮罩层\r\n    this.$nextTick(() => {\r\n      setTimeout(() => {\r\n        LoadingFix.forceClearMasks();\r\n        this.tableLoading = false;\r\n        console.log('已使用 LoadingFix 重置页面loading状态');\r\n      }, 100);\r\n    });\r\n\r\n    // 启动自动清理监听\r\n    this.maskObserver = LoadingFix.startAutoCleanup();\r\n  },\r\n\r\n  methods: {\r\n    // 获取数据时的回调\r\n    getDatas(data) {\r\n      this.tableData = data;\r\n      this.tableLoading = false; // 确保数据加载完成后隐藏loading\r\n    },\r\n\r\n    // 分配租户\r\n    handleAssignTenant(row) {\r\n      this.currentTerminal = Object.assign({}, row);\r\n      this.assignForm = {\r\n        terminalId: row.terminalId,\r\n        terminalName: row.terminalName,\r\n        tenantId: ''\r\n      };\r\n      this.dialogTitle = '分配租户';\r\n      this.assignDialogVisible = true;\r\n      this.loadTenantList();\r\n    },\r\n\r\n    // 更改租户\r\n    handleChangeTenant(row) {\r\n      this.currentTerminal = Object.assign({}, row);\r\n      this.assignForm = {\r\n        terminalId: row.terminalId,\r\n        terminalName: row.terminalName,\r\n        tenantId: row.tenantId || ''\r\n      };\r\n      this.dialogTitle = '更改租户';\r\n      this.assignDialogVisible = true;\r\n      this.loadTenantList();\r\n    },\r\n\r\n    // 加载租户列表\r\n    loadTenantList() {\r\n      this.$api['tenant/tenant-list']().then(res => {\r\n        this.tenantList = res.data || [];\r\n      }).catch(error => {\r\n        console.error('加载租户列表失败:', error);\r\n        this.tenantList = [];\r\n      });\r\n    },\r\n\r\n    // 确认分配\r\n    confirmAssign() {\r\n      this.$refs.assignForm.validate(valid => {\r\n        if (valid) {\r\n          this.submitLoading = true;\r\n          const apiData = {\r\n            terminalId: this.assignForm.terminalId,\r\n            tenantId: this.assignForm.tenantId\r\n          };\r\n\r\n          this.$api['terminal/assign-tenant'](apiData).then(() => {\r\n            this.$message({\r\n              message: '分配成功',\r\n              type: 'success'\r\n            });\r\n            this.assignDialogVisible = false;\r\n            this.$refs.grid.query();\r\n          }).catch(error => {\r\n            console.error('分配失败:', error);\r\n          }).finally(() => {\r\n            this.submitLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n\r\n    resetTable() {\r\n      this.searchForm = Object.assign({}, defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n\r\n    getColumn(e) {\r\n      this.columns = e;\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped></style>\r\n"]}]}