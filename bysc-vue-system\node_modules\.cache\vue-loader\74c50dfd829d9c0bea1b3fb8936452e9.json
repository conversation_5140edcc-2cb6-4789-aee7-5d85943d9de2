{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\AssignTenantDialog.vue?vue&type=template&id=6561ba54&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\AssignTenantDialog.vue", "mtime": 1753841449070}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745221315417}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\n<el-dialog\n  title=\"分配租户\"\n  :visible.sync=\"dialogVisible\"\n  width=\"500px\"\n  :close-on-click-modal=\"false\"\n  @close=\"handleClose\"\n>\n  <el-form :model=\"form\" :rules=\"rules\" ref=\"assignForm\" label-width=\"100px\">\n    <el-form-item label=\"终端ID\">\n      <el-input v-model=\"form.terminalId\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"终端名称\">\n      <el-input v-model=\"form.terminalName\" disabled></el-input>\n    </el-form-item>\n    <el-form-item label=\"选择租户\" prop=\"tenantId\">\n      <el-select \n        v-model=\"form.tenantId\" \n        placeholder=\"请选择租户\" \n        style=\"width: 100%\"\n        filterable\n      >\n        <el-option\n          v-for=\"tenant in tenantList\"\n          :key=\"tenant.id\"\n          :label=\"tenant.tenantName\"\n          :value=\"tenant.id\">\n        </el-option>\n      </el-select>\n    </el-form-item>\n    \n    <!-- 注意提示 -->\n    <el-alert\n      title=\"注意：更改租户后，该设备本地数据（用户、班组、策略）将会被清空，需要到新租户上进行重新配置操作！\"\n      type=\"warning\"\n      :closable=\"false\"\n      show-icon\n      style=\"margin-bottom: 20px;\">\n    </el-alert>\n  </el-form>\n  \n  <div slot=\"footer\" class=\"dialog-footer\">\n    <el-button @click=\"handleCancel\">取 消</el-button>\n    <el-button type=\"primary\" @click=\"handleConfirm\" :loading=\"loading\">确 定</el-button>\n  </div>\n</el-dialog>\n", null]}