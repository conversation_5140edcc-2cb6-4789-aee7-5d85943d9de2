{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\AssignTenantDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\components\\AssignTenantDialog.vue", "mtime": 1753841449070}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["export default {\n  name: 'AssignTenantDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    terminalInfo: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    tenantList: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      form: {\n        terminalId: '',\n        terminalName: '',\n        tenantId: ''\n      },\n      rules: {\n        tenantId: [{\n          required: true,\n          message: '请选择租户',\n          trigger: 'change'\n        }]\n      }\n    };\n  },\n  computed: {\n    dialogVisible: {\n      get: function get() {\n        return this.visible;\n      },\n      set: function set(val) {\n        this.$emit('update:visible', val);\n      }\n    }\n  },\n  watch: {\n    visible: function visible(val) {\n      if (val) {\n        this.initForm();\n      }\n    },\n    terminalInfo: {\n      handler: function handler(val) {\n        if (val && this.visible) {\n          this.initForm();\n        }\n      },\n      deep: true\n    }\n  },\n  methods: {\n    initForm: function initForm() {\n      var _this = this;\n      this.form = {\n        terminalId: this.terminalInfo.terminalId || '',\n        terminalName: this.terminalInfo.terminalName || '',\n        tenantId: ''\n      };\n      // 清除验证\n      this.$nextTick(function () {\n        if (_this.$refs.assignForm) {\n          _this.$refs.assignForm.clearValidate();\n        }\n      });\n    },\n    handleConfirm: function handleConfirm() {\n      var _this2 = this;\n      this.$refs.assignForm.validate(function (valid) {\n        if (valid) {\n          var data = {\n            terminalId: _this2.form.terminalId,\n            tenantId: _this2.form.tenantId\n          };\n          _this2.$emit('confirm', data);\n        }\n      });\n    },\n    handleCancel: function handleCancel() {\n      this.dialogVisible = false;\n    },\n    handleClose: function handleClose() {\n      this.form = {\n        terminalId: '',\n        terminalName: '',\n        tenantId: ''\n      };\n      if (this.$refs.assignForm) {\n        this.$refs.assignForm.clearValidate();\n      }\n      this.$emit('close');\n    }\n  }\n};", {"version": 3, "names": ["name", "props", "visible", "type", "Boolean", "default", "terminalInfo", "Object", "tenantList", "Array", "loading", "data", "form", "terminalId", "terminalName", "tenantId", "rules", "required", "message", "trigger", "computed", "dialogVisible", "get", "set", "val", "$emit", "watch", "initForm", "handler", "deep", "methods", "_this", "$nextTick", "$refs", "assignForm", "clearValidate", "handleConfirm", "_this2", "validate", "valid", "handleCancel", "handleClose"], "sources": ["src/bysc_system/views/terminalAssignment/components/AssignTenantDialog.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    title=\"分配租户\"\n    :visible.sync=\"dialogVisible\"\n    width=\"500px\"\n    :close-on-click-modal=\"false\"\n    @close=\"handleClose\"\n  >\n    <el-form :model=\"form\" :rules=\"rules\" ref=\"assignForm\" label-width=\"100px\">\n      <el-form-item label=\"终端ID\">\n        <el-input v-model=\"form.terminalId\" disabled></el-input>\n      </el-form-item>\n      <el-form-item label=\"终端名称\">\n        <el-input v-model=\"form.terminalName\" disabled></el-input>\n      </el-form-item>\n      <el-form-item label=\"选择租户\" prop=\"tenantId\">\n        <el-select \n          v-model=\"form.tenantId\" \n          placeholder=\"请选择租户\" \n          style=\"width: 100%\"\n          filterable\n        >\n          <el-option\n            v-for=\"tenant in tenantList\"\n            :key=\"tenant.id\"\n            :label=\"tenant.tenantName\"\n            :value=\"tenant.id\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      \n      <!-- 注意提示 -->\n      <el-alert\n        title=\"注意：更改租户后，该设备本地数据（用户、班组、策略）将会被清空，需要到新租户上进行重新配置操作！\"\n        type=\"warning\"\n        :closable=\"false\"\n        show-icon\n        style=\"margin-bottom: 20px;\">\n      </el-alert>\n    </el-form>\n    \n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleCancel\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"handleConfirm\" :loading=\"loading\">确 定</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nexport default {\n  name: 'AssignTenantDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    terminalInfo: {\n      type: Object,\n      default: () => ({})\n    },\n    tenantList: {\n      type: Array,\n      default: () => []\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      form: {\n        terminalId: '',\n        terminalName: '',\n        tenantId: ''\n      },\n      rules: {\n        tenantId: [\n          { required: true, message: '请选择租户', trigger: 'change' }\n        ]\n      }\n    };\n  },\n  computed: {\n    dialogVisible: {\n      get() {\n        return this.visible;\n      },\n      set(val) {\n        this.$emit('update:visible', val);\n      }\n    }\n  },\n  watch: {\n    visible(val) {\n      if (val) {\n        this.initForm();\n      }\n    },\n    terminalInfo: {\n      handler(val) {\n        if (val && this.visible) {\n          this.initForm();\n        }\n      },\n      deep: true\n    }\n  },\n  methods: {\n    initForm() {\n      this.form = {\n        terminalId: this.terminalInfo.terminalId || '',\n        terminalName: this.terminalInfo.terminalName || '',\n        tenantId: ''\n      };\n      // 清除验证\n      this.$nextTick(() => {\n        if (this.$refs.assignForm) {\n          this.$refs.assignForm.clearValidate();\n        }\n      });\n    },\n    \n    handleConfirm() {\n      this.$refs.assignForm.validate((valid) => {\n        if (valid) {\n          const data = {\n            terminalId: this.form.terminalId,\n            tenantId: this.form.tenantId\n          };\n          this.$emit('confirm', data);\n        }\n      });\n    },\n    \n    handleCancel() {\n      this.dialogVisible = false;\n    },\n    \n    handleClose() {\n      this.form = {\n        terminalId: '',\n        terminalName: '',\n        tenantId: ''\n      };\n      if (this.$refs.assignForm) {\n        this.$refs.assignForm.clearValidate();\n      }\n      this.$emit('close');\n    }\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\n.dialog-footer {\n  text-align: right;\n}\n\n::v-deep .el-alert__content {\n  line-height: 1.5;\n}\n</style>\n"], "mappings": "AAiDA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAG,UAAA;MACAL,IAAA,EAAAM,KAAA;MACAJ,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAK,OAAA;MACAP,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,UAAA;QACAC,YAAA;QACAC,QAAA;MACA;MACAC,KAAA;QACAD,QAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,QAAA;IACAC,aAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAApB,OAAA;MACA;MACAqB,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,mBAAAD,GAAA;MACA;IACA;EACA;EACAE,KAAA;IACAxB,OAAA,WAAAA,QAAAsB,GAAA;MACA,IAAAA,GAAA;QACA,KAAAG,QAAA;MACA;IACA;IACArB,YAAA;MACAsB,OAAA,WAAAA,QAAAJ,GAAA;QACA,IAAAA,GAAA,SAAAtB,OAAA;UACA,KAAAyB,QAAA;QACA;MACA;MACAE,IAAA;IACA;EACA;EACAC,OAAA;IACAH,QAAA,WAAAA,SAAA;MAAA,IAAAI,KAAA;MACA,KAAAnB,IAAA;QACAC,UAAA,OAAAP,YAAA,CAAAO,UAAA;QACAC,YAAA,OAAAR,YAAA,CAAAQ,YAAA;QACAC,QAAA;MACA;MACA;MACA,KAAAiB,SAAA;QACA,IAAAD,KAAA,CAAAE,KAAA,CAAAC,UAAA;UACAH,KAAA,CAAAE,KAAA,CAAAC,UAAA,CAAAC,aAAA;QACA;MACA;IACA;IAEAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAAJ,KAAA,CAAAC,UAAA,CAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAA5B,IAAA;YACAE,UAAA,EAAAwB,MAAA,CAAAzB,IAAA,CAAAC,UAAA;YACAE,QAAA,EAAAsB,MAAA,CAAAzB,IAAA,CAAAG;UACA;UACAsB,MAAA,CAAAZ,KAAA,YAAAd,IAAA;QACA;MACA;IACA;IAEA6B,YAAA,WAAAA,aAAA;MACA,KAAAnB,aAAA;IACA;IAEAoB,WAAA,WAAAA,YAAA;MACA,KAAA7B,IAAA;QACAC,UAAA;QACAC,YAAA;QACAC,QAAA;MACA;MACA,SAAAkB,KAAA,CAAAC,UAAA;QACA,KAAAD,KAAA,CAAAC,UAAA,CAAAC,aAAA;MACA;MACA,KAAAV,KAAA;IACA;EACA;AACA", "ignoreList": []}]}